/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as AuthRouteImport } from './routes/auth'
import { Route as DashboardRouteRouteImport } from './routes/dashboard/route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as LessonIndexRouteImport } from './routes/lesson/index'
import { Route as DashboardIndexRouteImport } from './routes/dashboard/index'
import { Route as DashboardCoursesIndexRouteImport } from './routes/dashboard/courses/index'
import { Route as DashboardAnalyticsIndexRouteImport } from './routes/dashboard/analytics/index'
import { Route as DashboardCoursesCourseSlugIndexRouteImport } from './routes/dashboard/courses/$courseSlug/index'

const AuthRoute = AuthRouteImport.update({
  id: '/auth',
  path: '/auth',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardRouteRoute = DashboardRouteRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const LessonIndexRoute = LessonIndexRouteImport.update({
  id: '/lesson/',
  path: '/lesson/',
  getParentRoute: () => rootRouteImport,
} as any)
const DashboardIndexRoute = DashboardIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DashboardCoursesIndexRoute = DashboardCoursesIndexRouteImport.update({
  id: '/courses/',
  path: '/courses/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DashboardAnalyticsIndexRoute = DashboardAnalyticsIndexRouteImport.update({
  id: '/analytics/',
  path: '/analytics/',
  getParentRoute: () => DashboardRouteRoute,
} as any)
const DashboardCoursesCourseSlugIndexRoute =
  DashboardCoursesCourseSlugIndexRouteImport.update({
    id: '/courses/$courseSlug/',
    path: '/courses/$courseSlug/',
    getParentRoute: () => DashboardRouteRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRouteRouteWithChildren
  '/auth': typeof AuthRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/lesson': typeof LessonIndexRoute
  '/dashboard/analytics': typeof DashboardAnalyticsIndexRoute
  '/dashboard/courses': typeof DashboardCoursesIndexRoute
  '/dashboard/courses/$courseSlug': typeof DashboardCoursesCourseSlugIndexRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/auth': typeof AuthRoute
  '/dashboard': typeof DashboardIndexRoute
  '/lesson': typeof LessonIndexRoute
  '/dashboard/analytics': typeof DashboardAnalyticsIndexRoute
  '/dashboard/courses': typeof DashboardCoursesIndexRoute
  '/dashboard/courses/$courseSlug': typeof DashboardCoursesCourseSlugIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/dashboard': typeof DashboardRouteRouteWithChildren
  '/auth': typeof AuthRoute
  '/dashboard/': typeof DashboardIndexRoute
  '/lesson/': typeof LessonIndexRoute
  '/dashboard/analytics/': typeof DashboardAnalyticsIndexRoute
  '/dashboard/courses/': typeof DashboardCoursesIndexRoute
  '/dashboard/courses/$courseSlug/': typeof DashboardCoursesCourseSlugIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | '/dashboard'
    | '/auth'
    | '/dashboard/'
    | '/lesson'
    | '/dashboard/analytics'
    | '/dashboard/courses'
    | '/dashboard/courses/$courseSlug'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | '/auth'
    | '/dashboard'
    | '/lesson'
    | '/dashboard/analytics'
    | '/dashboard/courses'
    | '/dashboard/courses/$courseSlug'
  id:
    | '__root__'
    | '/'
    | '/dashboard'
    | '/auth'
    | '/dashboard/'
    | '/lesson/'
    | '/dashboard/analytics/'
    | '/dashboard/courses/'
    | '/dashboard/courses/$courseSlug/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  DashboardRouteRoute: typeof DashboardRouteRouteWithChildren
  AuthRoute: typeof AuthRoute
  LessonIndexRoute: typeof LessonIndexRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/auth': {
      id: '/auth'
      path: '/auth'
      fullPath: '/auth'
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard': {
      id: '/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof DashboardRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/lesson/': {
      id: '/lesson/'
      path: '/lesson'
      fullPath: '/lesson'
      preLoaderRoute: typeof LessonIndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/dashboard/': {
      id: '/dashboard/'
      path: '/'
      fullPath: '/dashboard/'
      preLoaderRoute: typeof DashboardIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/courses/': {
      id: '/dashboard/courses/'
      path: '/courses'
      fullPath: '/dashboard/courses'
      preLoaderRoute: typeof DashboardCoursesIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/analytics/': {
      id: '/dashboard/analytics/'
      path: '/analytics'
      fullPath: '/dashboard/analytics'
      preLoaderRoute: typeof DashboardAnalyticsIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
    '/dashboard/courses/$courseSlug/': {
      id: '/dashboard/courses/$courseSlug/'
      path: '/courses/$courseSlug'
      fullPath: '/dashboard/courses/$courseSlug'
      preLoaderRoute: typeof DashboardCoursesCourseSlugIndexRouteImport
      parentRoute: typeof DashboardRouteRoute
    }
  }
}

interface DashboardRouteRouteChildren {
  DashboardIndexRoute: typeof DashboardIndexRoute
  DashboardAnalyticsIndexRoute: typeof DashboardAnalyticsIndexRoute
  DashboardCoursesIndexRoute: typeof DashboardCoursesIndexRoute
  DashboardCoursesCourseSlugIndexRoute: typeof DashboardCoursesCourseSlugIndexRoute
}

const DashboardRouteRouteChildren: DashboardRouteRouteChildren = {
  DashboardIndexRoute: DashboardIndexRoute,
  DashboardAnalyticsIndexRoute: DashboardAnalyticsIndexRoute,
  DashboardCoursesIndexRoute: DashboardCoursesIndexRoute,
  DashboardCoursesCourseSlugIndexRoute: DashboardCoursesCourseSlugIndexRoute,
}

const DashboardRouteRouteWithChildren = DashboardRouteRoute._addFileChildren(
  DashboardRouteRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  DashboardRouteRoute: DashboardRouteRouteWithChildren,
  AuthRoute: AuthRoute,
  LessonIndexRoute: LessonIndexRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
