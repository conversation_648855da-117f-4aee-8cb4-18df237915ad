import { createFileRoute, redirect } from "@tanstack/react-router";
import { requireAuth } from "@/utils/auth-guard";
export const Route = createFileRoute("/")({
  // beforeLoad: async () => {
  //   const auth = await requireAuth();
  //   if (auth) {
  //     throw redirect({ to: "/dashboard" });
  //   }
  // },
  component: Home,
});

function Home() {
  return (
    <div>
      <h1>Home</h1>
    </div>
  );
}
