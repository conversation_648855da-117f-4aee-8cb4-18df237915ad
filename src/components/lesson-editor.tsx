import type React from "react";
import { useState } from "react";
import type {
  LayoutComponent,
  LayoutTemplate,
  SelectedZone,
  DeleteConfirm,
  PickerPosition,
} from "./lesson-editor/types";
import {
  useLessonState,
  useHistory,
  useDragAndDrop,
} from "./lesson-editor/hooks";
import {
  calculateSmartPickerPosition,
  countComponentsInSection,
} from "./lesson-editor/utils";
import {
  LessonHeader,
  LessonSidebar,
  FloatingComponentPicker,
  AddSectionDialog,
  DeleteConfirmDialog,
  SlideRenderer,
} from "./lesson-editor/components";

export default function LessonEditor() {
  // State management hooks
  const {
    currentLesson,
    currentSlide,
    currentSlideIndex,
    setCurrentSlideIndex,
    addSlide,
    removeSlide,
    addSectionToSlide,
    removeSectionFromSlide,
    addComponentToZone,
    removeComponentFromZone,
    updateComponentContent,
  } = useLessonState();

  const { saveToHistory, undo, redo, canUndo, canRedo } = useHistory();

  const {
    draggedComponent,
    dragOverZone,
    handleDragStart,
    handleDragOver,
    handleDragOverWithFeedback,
    handleDrop,
    handleDragEnd,
    handleDragLeave,
  } = useDragAndDrop();

  // UI state
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [editingComponent, setEditingComponent] = useState<string | null>(null);
  const [selectedZone, setSelectedZone] = useState<SelectedZone | null>(null);
  const [showFloatingPicker, setShowFloatingPicker] = useState(false);
  const [pickerPosition, setPickerPosition] = useState<PickerPosition>({
    x: 0,
    y: 0,
  });
  const [showSectionDialog, setShowSectionDialog] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState<DeleteConfirm | null>(
    null
  );

  // Event handlers
  const handleUndo = () => {
    const previousLesson = undo();
    if (previousLesson) {
      // Update lesson state through the hook
    }
  };

  const handleRedo = () => {
    const nextLesson = redo();
    if (nextLesson) {
      // Update lesson state through the hook
    }
  };

  const handleAddSlide = () => {
    const newLesson = addSlide();
    saveToHistory(newLesson);
  };

  const handleDeleteSlide = (slideIndex: number) => {
    confirmDeleteSlide(slideIndex);
  };

  const handleAddSection = (template: LayoutTemplate) => {
    const newLesson = addSectionToSlide(template);
    saveToHistory(newLesson);
  };

  const handleDeleteSection = (sectionId: string) => {
    confirmDeleteSection(sectionId);
  };

  const handleAddComponent = (
    sectionId: string,
    zoneId: string,
    type: LayoutComponent["type"]
  ) => {
    const newLesson = addComponentToZone(sectionId, zoneId, type);
    saveToHistory(newLesson);
  };

  const handleDeleteComponent = (
    sectionId: string,
    zoneId: string,
    componentId: string
  ) => {
    confirmDeleteComponent(sectionId, zoneId, componentId);
  };

  const handleUpdateComponent = (
    sectionId: string,
    zoneId: string,
    componentId: string,
    content: string
  ) => {
    const newLesson = updateComponentContent(
      sectionId,
      zoneId,
      componentId,
      content
    );
    saveToHistory(newLesson);
  };

  const handleZoneClick = (
    event: React.MouseEvent,
    sectionId: string,
    zoneId: string
  ) => {
    event.stopPropagation();

    if (
      selectedZone?.sectionId === sectionId &&
      selectedZone?.zoneId === zoneId
    ) {
      setSelectedZone(null);
      setShowFloatingPicker(false);
      return;
    }

    const rect = event.currentTarget.getBoundingClientRect();
    const position = calculateSmartPickerPosition(rect);

    setPickerPosition(position);
    setSelectedZone({ sectionId, zoneId });
    setShowFloatingPicker(true);
  };

  const handlePickerClose = () => {
    setSelectedZone(null);
    setShowFloatingPicker(false);
  };

  const toggleEditMode = (componentId: string) => {
    setEditingComponent(editingComponent === componentId ? null : componentId);
  };

  // Helper functions for delete confirmations
  const confirmDeleteSlide = (slideIndex: number) => {
    if (currentLesson.slides.length <= 1) return;

    const slide = currentLesson.slides[slideIndex];
    const componentCount = slide.sections.reduce(
      (total, section) => total + countComponentsInSection(section),
      0
    );

    setDeleteConfirm({
      type: "slide",
      data: { slideIndex },
      title: "Delete Slide",
      description: `Are you sure you want to delete "Slide ${
        slideIndex + 1
      }"? This will also delete ${slide.sections.length} section${
        slide.sections.length !== 1 ? "s" : ""
      } and ${componentCount} component${
        componentCount !== 1 ? "s" : ""
      } inside it. This action cannot be undone.`,
    });
  };

  const confirmDeleteSection = (sectionId: string) => {
    const section = currentSlide.sections.find((s) => s.id === sectionId);
    if (!section) return;

    const componentCount = countComponentsInSection(section);

    setDeleteConfirm({
      type: "section",
      data: { sectionId },
      title: "Delete Section",
      description: `Are you sure you want to delete "${
        section.name
      }"? This will also delete ${componentCount} component${
        componentCount !== 1 ? "s" : ""
      } inside it. This action cannot be undone.`,
    });
  };

  const confirmDeleteComponent = (
    sectionId: string,
    zoneId: string,
    componentId: string
  ) => {
    const component = currentSlide.sections
      .find((s) => s.id === sectionId)
      ?.zones.find((z) => z.id === zoneId)
      ?.components.find((c) => c.id === componentId);

    if (!component) return;

    setDeleteConfirm({
      type: "component",
      data: { sectionId, zoneId, componentId },
      title: "Delete Component",
      description: `Are you sure you want to delete this ${component.type} component? This action cannot be undone.`,
    });
  };

  const handleConfirmDelete = () => {
    if (!deleteConfirm) return;

    const { type, data } = deleteConfirm;

    switch (type) {
      case "component": {
        const componentLesson = removeComponentFromZone(
          data.sectionId,
          data.zoneId,
          data.componentId
        );
        saveToHistory(componentLesson);
        break;
      }
      case "section": {
        const sectionLesson = removeSectionFromSlide(data.sectionId);
        saveToHistory(sectionLesson);
        break;
      }
      case "slide": {
        const slideLesson = removeSlide(data.slideIndex);
        saveToHistory(slideLesson);
        break;
      }
    }

    setDeleteConfirm(null);
  };

  return (
    <div className="relative min-h-screen bg-gray-50 pt-[80px]">
      <LessonHeader
        lesson={currentLesson}
        isPreviewMode={isPreviewMode}
        canUndo={canUndo}
        canRedo={canRedo}
        onUndo={handleUndo}
        onRedo={handleRedo}
        onTogglePreview={() => setIsPreviewMode(!isPreviewMode)}
        onSave={() => console.log("Save lesson")}
      />

      <div className="flex">
        {!isPreviewMode && (
          <LessonSidebar
            lesson={currentLesson}
            currentSlide={currentSlide}
            currentSlideIndex={currentSlideIndex}
            onSlideSelect={setCurrentSlideIndex}
            onAddSlide={handleAddSlide}
            onDeleteSlide={handleDeleteSlide}
          />
        )}

        <main
          className={`flex-1 bg-gray-50 p-6 ${
            isPreviewMode ? "mx-auto max-w-4xl" : "ml-80"
          }`}
          onClick={() => setShowFloatingPicker(false)}
        >
          <div className="space-y-8">
            {!isPreviewMode && currentSlide.sections.length > 0 && (
              <div className="flex items-center justify-between">
                <h2 className="font-sans font-semibold text-gray-900 text-xl">
                  {currentSlide.name}
                </h2>
                <p className="text-gray-600 text-sm">
                  Click zones to add components • Drag to reorder • Click edit
                  for rich text
                </p>
              </div>
            )}

            <SlideRenderer
              slide={currentSlide}
              isPreviewMode={isPreviewMode}
              onAddSection={() => setShowSectionDialog(true)}
              onDeleteSection={handleDeleteSection}
              onZoneClick={handleZoneClick}
              onAddComponent={handleAddComponent}
              onDeleteComponent={handleDeleteComponent}
              onUpdateComponent={handleUpdateComponent}
              onToggleEdit={toggleEditMode}
              editingComponent={editingComponent}
              draggedComponent={draggedComponent}
              dragOverZone={dragOverZone}
              onDragStart={handleDragStart}
              onDragOver={handleDragOver}
              onDragOverWithFeedback={handleDragOverWithFeedback}
              onDrop={(e, sectionId, zoneId, index) => {
                const updatedSlide = handleDrop(
                  e,
                  sectionId,
                  zoneId,
                  index,
                  currentSlide
                );
                if (updatedSlide) {
                  saveToHistory(currentLesson);
                }
              }}
              onDragEnd={handleDragEnd}
              onDragLeave={handleDragLeave}
            />
          </div>
        </main>
      </div>

      <AddSectionDialog
        isOpen={showSectionDialog}
        onClose={() => setShowSectionDialog(false)}
        onAddSection={handleAddSection}
      />

      <DeleteConfirmDialog
        deleteConfirm={deleteConfirm}
        onConfirm={handleConfirmDelete}
        onCancel={() => setDeleteConfirm(null)}
      />

      <FloatingComponentPicker
        isVisible={showFloatingPicker}
        selectedZone={selectedZone}
        position={pickerPosition}
        onAddComponent={handleAddComponent}
        onClose={handlePickerClose}
      />
    </div>
  );
}
