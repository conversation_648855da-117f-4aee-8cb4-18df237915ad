import {
  <PERSON><PERSON><PERSON>,
  <PERSON>umns,
  Edit3,
  Eye,
  FileText,
  Gamepad2,
  Grid,
  GripVertical,
  HelpCircle,
  ImageIcon,
  LayoutGrid,
  Plus,
  Redo,
  Rows,
  Save,
  Square,
  Trash2,
  Type,
  Undo,
  Video,
} from "lucide-react";
import type React from "react";
import { useState, useRef } from "react";
import { TiptapEditor } from "@/components/tiptap-editor";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";

interface LayoutZone {
  id: string;
  components: LayoutComponent[];
}

interface LayoutTemplate {
  id: string;
  name: string;
  icon: any;
  description: string;
  zones: LayoutZone[];
  gridClass: string;
}

interface LayoutSection {
  id: string;
  name: string;
  template: LayoutTemplate;
  zones: LayoutZone[];
}

interface Slide {
  id: string;
  name: string;
  sections: LayoutSection[];
  thumbnail?: string;
}

interface Lesson {
  id: string;
  title: string;
  description: string;
  slides: Slide[];
}

interface LayoutComponent {
  id: string;
  type: "text" | "image" | "video" | "card" | "grid" | "game" | "quiz";
  content: string;
  color?: string;
  size?: "small" | "medium" | "large";
  gameData?: any;
  quizData?: any;
}

const layoutTemplates: LayoutTemplate[] = [
  {
    id: "single",
    name: "Single Column",
    icon: Square,
    description: "One full-width content area",
    zones: [{ id: "zone-1", components: [] }],
    gridClass: "grid grid-cols-1 gap-6",
  },
  {
    id: "two-column",
    name: "Two Columns",
    icon: Columns,
    description: "Side-by-side layout",
    zones: [
      { id: "zone-1", components: [] },
      { id: "zone-2", components: [] },
    ],
    gridClass: "grid grid-cols-2 gap-6",
  },
  {
    id: "three-column",
    name: "Three Columns",
    icon: Grid,
    description: "Triple column layout",
    zones: [
      { id: "zone-1", components: [] },
      { id: "zone-2", components: [] },
      { id: "zone-3", components: [] },
    ],
    gridClass: "grid grid-cols-3 gap-6",
  },
  {
    id: "hero-two",
    name: "Hero + Two Columns",
    icon: Rows,
    description: "Full-width header with two columns below",
    zones: [
      { id: "zone-1", components: [] },
      { id: "zone-2", components: [] },
      { id: "zone-3", components: [] },
    ],
    gridClass: "grid grid-cols-2 gap-6",
  },
  {
    id: "grid-four",
    name: "Four Grid",
    icon: LayoutGrid,
    description: "2x2 grid layout",
    zones: [
      { id: "zone-1", components: [] },
      { id: "zone-2", components: [] },
      { id: "zone-3", components: [] },
      { id: "zone-4", components: [] },
    ],
    gridClass: "grid grid-cols-2 gap-6",
  },
];

const componentTypes = [
  {
    type: "text",
    icon: Type,
    label: "Text Block",
    color: "bg-blue-100 text-blue-800",
  },
  {
    type: "image",
    icon: ImageIcon,
    label: "Image",
    color: "bg-green-100 text-green-800",
  },
  {
    type: "video",
    icon: Video,
    label: "Video",
    color: "bg-purple-100 text-purple-800",
  },
  {
    type: "card",
    icon: FileText,
    label: "Content Card",
    color: "bg-orange-100 text-orange-800",
  },
  {
    type: "chart",
    icon: LayoutGrid,
    label: "Chart",
    color: "bg-teal-100 text-teal-800",
  },
  {
    type: "game",
    icon: Gamepad2,
    label: "Game",
    color: "bg-green-100 text-green-800",
  },
  {
    type: "quiz",
    icon: HelpCircle,
    label: "Quiz",
    color: "bg-purple-100 text-purple-800",
  },
];

// Generate stable IDs for SSR compatibility
let idCounter = 0;
const generateId = (prefix: string) => `${prefix}-${++idCounter}`;

// Color counter for consistent color selection
let colorCounter = 0;

const createDefaultSlide = (slideNumber: number): Slide => {
  const defaultTemplate = layoutTemplates.find((t) => t.id === "single")!;
  const defaultSection: LayoutSection = {
    id: generateId("section"),
    name: "Single Column Section",
    template: defaultTemplate,
    zones: [
      {
        id: generateId("zone"),
        components: [
          {
            id: generateId("text"),
            type: "text",
            content:
              "<h2>Welcome to your lesson</h2><p>Start editing this content to create your lesson material.</p>",
            color: "bg-white",
            size: "medium",
          },
        ],
      },
    ],
  };

  return {
    id: generateId("slide"),
    name: `Slide ${slideNumber}`,
    sections: [defaultSection],
  };
};

export default function LayoutBuilder() {
  const [currentLesson, setCurrentLesson] = useState<Lesson>({
    id: "lesson-1",
    title: "New Lesson",
    description: "Create engaging lesson content with multiple slides",
    slides: [createDefaultSlide(1)],
  });
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [history, setHistory] = useState<Lesson[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  const currentSlide = currentLesson.slides[currentSlideIndex];
  const [editingComponent, setEditingComponent] = useState<string | null>(null);
  const [selectedZone, setSelectedZone] = useState<{
    sectionId: string;
    zoneId: string;
  } | null>(null);
  const [showFloatingPicker, setShowFloatingPicker] = useState(false);
  const [pickerPosition, setPickerPosition] = useState({ x: 0, y: 0 });
  const [draggedComponent, setDraggedComponent] = useState<{
    sectionId: string;
    zoneId: string;
    componentId: string;
    index: number;
  } | null>(null);
  const [dragOverZone, setDragOverZone] = useState<{
    sectionId: string;
    zoneId: string;
    index: number;
  } | null>(null);
  const [showSectionDialog, setShowSectionDialog] = useState(false);
  const [deleteConfirm, setDeleteConfirm] = useState<{
    type: "component" | "section" | "slide";
    data: any;
    title: string;
    description: string;
  } | null>(null);

  const addSectionToSlide = (template: LayoutTemplate) => {
    const newSection: LayoutSection = {
      id: generateId("section"),
      name: `${template.name} Section`,
      template: template,
      zones: template.zones.map((zone) => ({ ...zone, components: [] })),
    };

    const updatedSlide = {
      ...currentSlide,
      sections: [...currentSlide.sections, newSection],
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, index) =>
        index === currentSlideIndex ? updatedSlide : slide,
      ),
    };

    setCurrentLesson(newLesson);
    saveToHistory(newLesson);
  };

  const addNewSlide = () => {
    const newSlide = createDefaultSlide(currentLesson.slides.length + 1);
    const newLesson = {
      ...currentLesson,
      slides: [...currentLesson.slides, newSlide],
    };

    setCurrentLesson(newLesson);
    setCurrentSlideIndex(newLesson.slides.length - 1);
    saveToHistory(newLesson);
  };

  const confirmDeleteSection = (sectionId: string) => {
    const section = currentSlide.sections.find((s) => s.id === sectionId);
    if (!section) return;

    const componentCount = section.zones.reduce(
      (total, zone) => total + zone.components.length,
      0,
    );

    setDeleteConfirm({
      type: "section",
      data: { sectionId },
      title: "Delete Section",
      description: `Are you sure you want to delete "${
        section.name
      }"? This will also delete ${componentCount} component${
        componentCount !== 1 ? "s" : ""
      } inside it. This action cannot be undone.`,
    });
  };

  const removeSectionFromSlide = (sectionId: string) => {
    const updatedSlide = {
      ...currentSlide,
      sections: currentSlide.sections.filter(
        (section) => section.id !== sectionId,
      ),
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, index) =>
        index === currentSlideIndex ? updatedSlide : slide,
      ),
    };

    setCurrentLesson(newLesson);
    saveToHistory(newLesson);
  };

  const confirmDeleteSlide = (slideIndex: number) => {
    if (currentLesson.slides.length <= 1) return; // Keep at least one slide

    const slide = currentLesson.slides[slideIndex];
    const componentCount = slide.sections.reduce(
      (total, section) =>
        total +
        section.zones.reduce(
          (zoneTotal, zone) => zoneTotal + zone.components.length,
          0,
        ),
      0,
    );

    setDeleteConfirm({
      type: "slide",
      data: { slideIndex },
      title: "Delete Slide",
      description: `Are you sure you want to delete "Slide ${
        slideIndex + 1
      }"? This will also delete ${slide.sections.length} section${
        slide.sections.length !== 1 ? "s" : ""
      } and ${componentCount} component${
        componentCount !== 1 ? "s" : ""
      } inside it. This action cannot be undone.`,
    });
  };

  const removeSlide = (slideIndex: number) => {
    if (currentLesson.slides.length <= 1) return; // Keep at least one slide

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.filter((_, index) => index !== slideIndex),
    };

    const newCurrentIndex =
      slideIndex === currentSlideIndex && slideIndex > 0
        ? slideIndex - 1
        : currentSlideIndex > slideIndex
          ? currentSlideIndex - 1
          : currentSlideIndex;

    setCurrentLesson(newLesson);
    setCurrentSlideIndex(
      Math.min(newCurrentIndex, newLesson.slides.length - 1),
    );
    saveToHistory(newLesson);
  };

  const addComponentToZone = (
    sectionId: string,
    zoneId: string,
    type: LayoutComponent["type"],
  ) => {
    const newComponent: LayoutComponent = {
      id: generateId(type),
      type,
      content: getDefaultContent(type),
      color: getRandomColor(),
      size: "medium",
    };

    if (type === "game") {
      newComponent.gameData = { gameType: "memory", config: {} };
    } else if (type === "quiz") {
      newComponent.quizData = {
        quizType: "multiple-choice",
        questions: [],
        config: {},
      };
    }

    const updatedSlide = {
      ...currentSlide,
      sections: currentSlide.sections.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              zones: section.zones.map((zone) =>
                zone.id === zoneId
                  ? { ...zone, components: [...zone.components, newComponent] }
                  : zone,
              ),
            }
          : section,
      ),
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, index) =>
        index === currentSlideIndex ? updatedSlide : slide,
      ),
    };

    setCurrentLesson(newLesson);
    saveToHistory(newLesson);
    setSelectedZone(null);
    setShowFloatingPicker(false);
  };

  const getDefaultContent = (type: string) => {
    switch (type) {
      case "text":
        return "Lorem ipsum dolor sit amet, consectetur adipiscing elit.";
      case "image":
        return "Image placeholder - Click to upload";
      case "video":
        return "Video placeholder - Click to embed";
      case "card":
        return "Content card with customizable information";
      case "grid":
        return "Grid layout for organizing multiple elements";
      case "game":
        return "Game component - Click to configure";
      case "quiz":
        return "Quiz component - Click to configure";
      default:
        return "Default content";
    }
  };

  const getRandomColor = () => {
    const colors = [
      "bg-yellow-100",
      "bg-green-100",
      "bg-purple-100",
      "bg-blue-100",
      "bg-orange-100",
      "bg-teal-100",
    ];
    return colors[colorCounter++ % colors.length];
  };

  const saveToHistory = (newLesson: Lesson) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push({ ...newLesson });
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  const undo = () => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      setCurrentLesson({ ...history[historyIndex - 1] });
    }
  };

  const redo = () => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      setCurrentLesson({ ...history[historyIndex + 1] });
    }
  };

  const confirmDeleteComponent = (
    sectionId: string,
    zoneId: string,
    componentId: string,
  ) => {
    const component = currentSlide.sections
      .find((s) => s.id === sectionId)
      ?.zones.find((z) => z.id === zoneId)
      ?.components.find((c) => c.id === componentId);

    if (!component) return;

    setDeleteConfirm({
      type: "component",
      data: { sectionId, zoneId, componentId },
      title: "Delete Component",
      description: `Are you sure you want to delete this ${component.type} component? This action cannot be undone.`,
    });
  };

  const removeComponentFromZone = (
    sectionId: string,
    zoneId: string,
    componentId: string,
  ) => {
    const updatedSlide = {
      ...currentSlide,
      sections: currentSlide.sections.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              zones: section.zones.map((zone) =>
                zone.id === zoneId
                  ? {
                      ...zone,
                      components: zone.components.filter(
                        (comp) => comp.id !== componentId,
                      ),
                    }
                  : zone,
              ),
            }
          : section,
      ),
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, index) =>
        index === currentSlideIndex ? updatedSlide : slide,
      ),
    };

    setCurrentLesson(newLesson);
    saveToHistory(newLesson);
  };

  const updateComponentContent = (
    sectionId: string,
    zoneId: string,
    componentId: string,
    newContent: string,
  ) => {
    const updatedSlide = {
      ...currentSlide,
      sections: currentSlide.sections.map((section) =>
        section.id === sectionId
          ? {
              ...section,
              zones: section.zones.map((zone) =>
                zone.id === zoneId
                  ? {
                      ...zone,
                      components: zone.components.map((comp) =>
                        comp.id === componentId
                          ? { ...comp, content: newContent }
                          : comp,
                      ),
                    }
                  : zone,
              ),
            }
          : section,
      ),
    };

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, index) =>
        index === currentSlideIndex ? updatedSlide : slide,
      ),
    };

    setCurrentLesson(newLesson);
    saveToHistory(newLesson);
  };

  const toggleEditMode = (componentId: string) => {
    setEditingComponent(editingComponent === componentId ? null : componentId);
  };

  const handleZoneClick = (
    event: React.MouseEvent,
    sectionId: string,
    zoneId: string,
  ) => {
    event.stopPropagation();

    if (
      selectedZone?.sectionId === sectionId &&
      selectedZone?.zoneId === zoneId
    ) {
      // Toggle off if clicking the same zone
      setSelectedZone(null);
      setShowFloatingPicker(false);
      return;
    }

    const rect = event.currentTarget.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;
    const pickerHeight = 350; // estimated picker height
    const pickerWidth = 250;

    // Smart positioning logic
    let x = rect.right + 15;
    let y = rect.top;

    // If picker would go off right edge, position on left side
    if (x + pickerWidth > viewportWidth) {
      x = rect.left - pickerWidth - 15;
    }

    // If picker would go off bottom, adjust y position
    if (y + pickerHeight > viewportHeight) {
      y = viewportHeight - pickerHeight - 20;
    }

    // Ensure picker doesn't go above viewport
    if (y < 20) {
      y = 20;
    }

    setPickerPosition({ x, y });
    setSelectedZone({ sectionId, zoneId });
    setShowFloatingPicker(true);
  };

  const handlePickerClose = () => {
    setSelectedZone(null);
    setShowFloatingPicker(false);
  };

  const handleDragStart = (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    componentId: string,
    index: number,
  ) => {
    e.stopPropagation();
    console.log("[v0] Drag start:", { sectionId, zoneId, componentId, index });
    setDraggedComponent({ sectionId, zoneId, componentId, index });
    e.dataTransfer.effectAllowed = "move";

    const dragImage = document.createElement("div");
    dragImage.innerHTML = `
      <div style="
        background: white; 
        border: 2px solid #e5e7eb; 
        border-radius: 8px; 
        padding: 8px 12px; 
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #6b7280;
      ">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="9" cy="12" r="1"/>
          <circle cx="9" cy="5" r="1"/>
          <circle cx="9" cy="19" r="1"/>
          <circle cx="15" cy="12" r="1"/>
          <circle cx="15" cy="5" r="1"/>
          <circle cx="15" cy="19" r="1"/>
        </svg>
        Moving component...
      </div>
    `;
    dragImage.style.position = "absolute";
    dragImage.style.top = "-1000px";
    document.body.appendChild(dragImage);

    e.dataTransfer.setDragImage(dragImage, 75, 20);

    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDragOverWithFeedback = (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    index: number,
  ) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = "move";
    setDragOverZone({ sectionId, zoneId, index });
  };

  const handleDrop = (
    e: React.DragEvent,
    targetSectionId: string,
    targetZoneId: string,
    targetIndex: number,
  ) => {
    e.preventDefault();
    e.stopPropagation();
    console.log("[v0] Drop:", {
      targetSectionId,
      targetZoneId,
      targetIndex,
      draggedComponent,
    });
    setDragOverZone(null);

    if (!draggedComponent) {
      console.log("[v0] No dragged component found");
      return;
    }

    const {
      sectionId: sourceSectionId,
      zoneId: sourceZoneId,
      componentId,
      index: sourceIndex,
    } = draggedComponent;

    if (
      sourceSectionId === targetSectionId &&
      sourceZoneId === targetZoneId &&
      sourceIndex === targetIndex
    ) {
      console.log("[v0] Dropped in same position");
      setDraggedComponent(null);
      return;
    }

    // Create a copy of the lesson
    let updatedSlide = { ...currentSlide };
    let movedComponent: LayoutComponent | null = null;

    // Remove component from source
    updatedSlide.sections = updatedSlide.sections.map((section) => {
      if (section.id === sourceSectionId) {
        return {
          ...section,
          zones: section.zones.map((zone) => {
            if (zone.id === sourceZoneId) {
              const newComponents = [...zone.components];
              [movedComponent] = newComponents.splice(sourceIndex, 1);
              return { ...zone, components: newComponents };
            }
            return zone;
          }),
        };
      }
      return section;
    });

    // Add component to target
    if (movedComponent) {
      updatedSlide.sections = updatedSlide.sections.map((section) => {
        if (section.id === targetSectionId) {
          return {
            ...section,
            zones: section.zones.map((zone) => {
              if (zone.id === targetZoneId) {
                const newComponents = [...zone.components];
                // Adjust index if moving within same zone
                const adjustedIndex =
                  sourceSectionId === targetSectionId &&
                  sourceZoneId === targetZoneId &&
                  sourceIndex < targetIndex
                    ? targetIndex - 1
                    : targetIndex;
                newComponents.splice(adjustedIndex, 0, movedComponent!);
                return { ...zone, components: newComponents };
              }
              return zone;
            }),
          };
        }
        return section;
      });
    }

    const newLesson = {
      ...currentLesson,
      slides: currentLesson.slides.map((slide, index) =>
        index === currentSlideIndex ? updatedSlide : slide,
      ),
    };

    setCurrentLesson(newLesson);
    saveToHistory(newLesson);
    setDraggedComponent(null);
  };

  const handleDragEnd = () => {
    console.log("[v0] Drag end");
    setDraggedComponent(null);
    setDragOverZone(null);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setDragOverZone(null);
    }
  };

  const handleConfirmDelete = () => {
    if (!deleteConfirm) return;

    const { type, data } = deleteConfirm;

    switch (type) {
      case "component":
        removeComponentFromZone(data.sectionId, data.zoneId, data.componentId);
        break;
      case "section":
        removeSectionFromSlide(data.sectionId);
        break;
      case "slide":
        removeSlide(data.slideIndex);
        break;
    }

    setDeleteConfirm(null);
  };

  return (
    <div className="relative min-h-screen bg-gray-50 pt-[80px]">
      {/* Add Section Dialog (for Layout slides only) */}
      <Dialog open={showSectionDialog} onOpenChange={setShowSectionDialog}>
        <DialogContent className="sm:max-w-xl">
          <DialogHeader>
            <DialogTitle>Add Layout Section</DialogTitle>
            <DialogDescription>
              Choose a layout template to add as a new section to your current
              slide.
            </DialogDescription>
          </DialogHeader>

          <div className="mt-4 grid grid-cols-2 gap-3">
            {layoutTemplates.map((template) => {
              const IconComponent = template.icon;
              return (
                <Button
                  key={template.id}
                  onClick={() => {
                    addSectionToSlide(template);
                    setShowSectionDialog(false);
                  }}
                  className="h-auto p-4"
                  variant="outline"
                >
                  <div className="text-center">
                    <IconComponent className="mx-auto mb-2 h-8 w-8 text-gray-600" />
                    <div className="font-medium text-sm">{template.name}</div>
                    <div className="mt-1 text-muted-foreground text-xs">
                      {template.description}
                    </div>
                  </div>
                </Button>
              );
            })}
          </div>

          <div className="mt-6 flex gap-2">
            <Button
              variant="ghost"
              onClick={() => setShowSectionDialog(false)}
              className="flex-1"
            >
              Cancel
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={!!deleteConfirm}
        onOpenChange={() => setDeleteConfirm(null)}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <Trash2 className="h-5 w-5" />
              {deleteConfirm?.title}
            </DialogTitle>
            <DialogDescription className="pt-2">
              {deleteConfirm?.description}
            </DialogDescription>
          </DialogHeader>

          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={() => setDeleteConfirm(null)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirmDelete}
              className="flex-1"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete
            </Button>
          </div>
        </DialogContent>
      </Dialog>

      <header className="fixed top-0 right-0 left-0 z-20 border-b bg-white shadow-sm">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <div className="mb-2 flex items-center gap-3">
              <BookOpen className="h-6 w-6 text-blue-600" />
              <h1 className="font-bold font-sans text-2xl text-gray-900">
                {currentLesson.title}
              </h1>
            </div>
            <p className="font-serif text-gray-600 text-sm">
              {currentLesson.description}
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={undo}
              disabled={historyIndex <= 0}
            >
              <Undo className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={redo}
              disabled={historyIndex >= history.length - 1}
            >
              <Redo className="h-4 w-4" />
            </Button>
            <Separator orientation="vertical" className="h-6" />
            <Button
              variant={isPreviewMode ? "default" : "outline"}
              size="sm"
              onClick={() => setIsPreviewMode(!isPreviewMode)}
            >
              <Eye className="mr-2 h-4 w-4" />
              {isPreviewMode ? "Edit" : "Preview"}
            </Button>
            <Button size="sm">
              <Save className="mr-2 h-4 w-4" />
              Save Lesson
            </Button>
          </div>
        </div>
      </header>

      <div className="flex">
        {!isPreviewMode && (
          <aside className="fixed top-[90px] left-0 z-10 flex h-[calc(100vh-80px)] w-80 flex-col overflow-hidden border-gray-200 border-r bg-white">
            {/* Slide Navigation */}
            <div className="border-gray-200 border-b p-4">
              <div className="mb-3 flex items-center justify-between">
                <h2 className="font-semibold text-gray-900 text-lg">Slides</h2>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={addNewSlide}
                  className="h-8"
                >
                  <Plus className="mr-1 h-4 w-4" />
                  New Slide
                </Button>
              </div>

              {/* Slide List */}
              <div className="max-h-[calc(60dvh)] space-y-2 overflow-y-auto">
                {currentLesson.slides.map((slide, index) => (
                  <div
                    key={slide.id}
                    className={`group cursor-pointer rounded-lg border-2 p-3 transition-all ${
                      index === currentSlideIndex
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 bg-gray-50 hover:border-gray-300 hover:bg-gray-100"
                    }`}
                    onClick={() => setCurrentSlideIndex(index)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-1 font-medium text-gray-900 text-sm">
                          <LayoutGrid className="h-3 w-3" />
                          Slide {index + 1}
                        </div>
                        <div className="text-gray-500 text-xs">
                          {slide.sections.length} section
                          {slide.sections.length !== 1 ? "s" : ""}
                        </div>
                      </div>

                      {currentLesson.slides.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            confirmDeleteSlide(index);
                          }}
                          className="h-6 w-6 p-0 text-red-600 opacity-0 hover:bg-red-50 hover:text-red-800 group-hover:opacity-100"
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Instructions & Overview */}
            <div className="flex flex-1 flex-col overflow-hidden p-4">
              <div className="space-y-4">
                {/* Instructions */}
                <div className="rounded-lg border border-blue-200 bg-blue-50 p-3">
                  <h4 className="mb-1 font-medium text-blue-900 text-sm">
                    💡 How to add content
                  </h4>
                  <p className="text-blue-700 text-xs">
                    Click the "Add Component" area at the bottom of zones to add
                    content. Drag components to reorder.
                  </p>
                </div>
              </div>

              {/* Overview Stats */}
              <div className="mt-auto border-gray-200 border-t pt-4">
                <h4 className="mb-3 font-medium text-gray-700 text-xs uppercase tracking-wide">
                  Current Slide Overview
                </h4>
                <div className="grid grid-cols-3 gap-2 text-center">
                  <div>
                    <div className="font-bold text-blue-600 text-lg">
                      {currentLesson.slides.length}
                    </div>
                    <div className="text-gray-500 text-xs">Total Slides</div>
                  </div>
                  <div>
                    <div className="font-bold text-green-600 text-lg">
                      {currentSlide.sections.length}
                    </div>
                    <div className="text-gray-500 text-xs">Sections</div>
                  </div>
                  <div>
                    <div className="font-bold text-lg text-purple-600">
                      {currentSlide.sections.reduce(
                        (total, section) =>
                          total +
                          section.zones.reduce(
                            (zoneTotal, zone) =>
                              zoneTotal + zone.components.length,
                            0,
                          ),
                        0,
                      )}
                    </div>
                    <div className="text-gray-500 text-xs">Components</div>
                  </div>
                </div>
              </div>
            </div>
          </aside>
        )}

        <main
          className={`flex-1 bg-gray-50 p-6 ${
            isPreviewMode ? "mx-auto max-w-4xl" : "ml-80"
          }`}
          onClick={() => setShowFloatingPicker(false)}
        >
          <div className="space-y-8">
            {!isPreviewMode && currentSlide.sections.length > 0 && (
              <div className="flex items-center justify-between">
                <h2 className="font-sans font-semibold text-gray-900 text-xl">
                  {currentSlide.name}
                </h2>
                <p className="text-gray-600 text-sm">
                  Click zones to add components • Drag to reorder • Click edit
                  for rich text
                </p>
              </div>
            )}

            {currentSlide.sections.length === 0 ? (
              <Card className="border-2 border-gray-300 border-dashed bg-white">
                <CardContent className="flex flex-col items-center justify-center py-16">
                  <LayoutGrid className="mb-4 h-12 w-12 text-gray-400" />
                  <h3 className="mb-2 font-medium text-gray-900 text-lg">
                    Build Your Layout Slide
                  </h3>
                  <p className="mb-4 max-w-md text-center text-gray-600">
                    Add layout sections to create rich content for this slide.
                  </p>
                  <Button
                    onClick={() => setShowSectionDialog(true)}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Add Your First Section
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-1">
                {currentSlide.sections.map((section, sectionIndex) => (
                  <div key={section.id} className="group relative">
                    {!isPreviewMode && (
                      <div className="mb-3 flex items-center justify-between rounded-t-lg border-gray-200 border-b bg-gray-50/80 px-4 py-2">
                        <Badge
                          variant="outline"
                          className="border-blue-200 bg-blue-50 text-blue-800"
                        >
                          <LayoutGrid className="mr-1 inline h-3 w-3" />
                          {section.name}
                        </Badge>
                        <div className="flex items-center gap-2 opacity-0 transition-opacity group-hover:opacity-100">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => confirmDeleteSection(section.id)}
                            className="text-red-600 hover:bg-red-50 hover:text-red-800"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    )}
                    {renderSection(section, sectionIndex)}
                  </div>
                ))}

                {/* Add Section area */}
                {!isPreviewMode && (
                  <div
                    className="group/add-section relative mt-6 flex min-h-[80px] cursor-pointer items-center justify-center rounded-lg border-2 border-gray-300 border-dashed bg-gray-50/50 py-6 transition-all hover:border-blue-400 hover:bg-blue-50/50"
                    onClick={() => setShowSectionDialog(true)}
                  >
                    <div className="flex flex-col items-center gap-2 text-gray-600 opacity-70 transition-opacity group-hover/add-section:text-blue-600 group-hover/add-section:opacity-100">
                      <LayoutGrid className="h-6 w-6" />
                      <span className="font-medium text-sm">Add Section</span>
                      <span className="text-gray-500 text-xs group-hover/add-section:text-blue-500">
                        Choose layout template
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </main>
      </div>

      {/* Floating Component Picker */}
      {showFloatingPicker && selectedZone && (
        <>
          {/* Backdrop to close picker */}
          <div
            className="fixed inset-0 z-40 bg-transparent"
            onClick={handlePickerClose}
          />

          {/* Floating Picker */}
          <div
            className="fade-in zoom-in-95 fixed z-50 w-64 animate-in rounded-lg border border-gray-300 bg-white shadow-2xl duration-200"
            style={{
              left: `${pickerPosition.x}px`,
              top: `${pickerPosition.y}px`,
            }}
          >
            <div className="rounded-t-lg border-gray-100 border-b bg-gray-50 p-4">
              <h3 className="font-semibold text-gray-900 text-sm">
                Add Component
              </h3>
              <p className="mt-1 text-gray-500 text-xs">
                Click a component to add it to this zone
              </p>
            </div>

            <div className="max-h-72 overflow-y-auto p-2">
              <div className="space-y-1">
                {componentTypes.map(({ type, icon: Icon, label, color }) => (
                  <Button
                    key={type}
                    variant="ghost"
                    className="h-auto w-full justify-start border border-transparent p-3 transition-colors hover:border-blue-200 hover:bg-blue-50"
                    onClick={() => {
                      addComponentToZone(
                        selectedZone.sectionId,
                        selectedZone.zoneId,
                        type as LayoutComponent["type"],
                      );
                      handlePickerClose();
                    }}
                  >
                    <Icon className="mr-3 h-4 w-4 text-gray-700" />
                    <div className="flex-1 text-left">
                      <div className="font-medium text-gray-900 text-sm">
                        {label}
                      </div>
                      <div className="mt-0.5 text-gray-500 text-xs">
                        {type === "text" && "Rich text content"}
                        {type === "image" && "Images and media"}
                        {type === "video" && "Video embeds"}
                        {type === "card" && "Content cards"}
                        {type === "chart" && "Charts and graphs"}
                        {type === "game" && "Interactive games"}
                        {type === "quiz" && "Quiz and assessments"}
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            <div className="rounded-b-lg border-gray-100 border-t bg-gray-50 p-3">
              <Button
                variant="ghost"
                size="sm"
                onClick={handlePickerClose}
                className="h-8 w-full text-gray-600 hover:text-gray-900"
              >
                Cancel
              </Button>
            </div>
          </div>
        </>
      )}
    </div>
  );

  function renderSection(section: LayoutSection, sectionIndex: number) {
    const isLastSection = currentSlide.sections.length - 1 === sectionIndex;
    const borderClass = isLastSection ? "rounded-b-lg border-b" : "border-b-0";

    return (
      <div
        className={
          section.template.id === "hero-two"
            ? `space-y-6 border-gray-200 border-r border-l bg-white p-6 ${borderClass} ${
                sectionIndex === 0 ? "rounded-t-lg border-t" : ""
              }`
            : `${
                section.template.gridClass
              } border-gray-200 border-r border-l bg-white p-6 ${borderClass} ${
                sectionIndex === 0 ? "rounded-t-lg border-t" : ""
              }`
        }
      >
        {section.template.id === "hero-two" ? (
          <>
            <div className="col-span-2">
              {renderZone(section, section.zones[0], 0)}
            </div>
            <div className="grid grid-cols-2 gap-6">
              {section.zones.slice(1).map((zone, index) => (
                <div key={zone.id}>{renderZone(section, zone, index + 1)}</div>
              ))}
            </div>
          </>
        ) : (
          section.zones.map((zone, index) => (
            <div key={zone.id}>{renderZone(section, zone, index)}</div>
          ))
        )}
      </div>
    );
  }

  function renderZone(section: LayoutSection, zone: LayoutZone, index: number) {
    if (zone.components.length === 0) {
      return (
        <div
          className={`group/empty-zone relative min-h-[140px] rounded-lg border-2 border-gray-300 border-dashed bg-gray-50/30 transition-all duration-300 hover:border-blue-300 hover:bg-blue-50/50 ${
            selectedZone?.sectionId === section.id &&
            selectedZone?.zoneId === zone.id
              ? "border-blue-500 bg-blue-50"
              : ""
          } ${
            dragOverZone?.sectionId === section.id &&
            dragOverZone?.zoneId === zone.id &&
            dragOverZone?.index === 0
              ? "border-blue-500 bg-blue-50"
              : ""
          }`}
          onDragOver={handleDragOver}
          onDragEnter={(e) =>
            handleDragOverWithFeedback(e, section.id, zone.id, 0)
          }
          onDrop={(e) => handleDrop(e, section.id, zone.id, 0)}
          onDragLeave={handleDragLeave}
        >
          {/* Default state */}
          <div className="flex min-h-[140px] items-center justify-center py-6 transition-opacity duration-200 group-hover/empty-zone:hidden">
            <div className="flex flex-col items-center">
              <Plus className="mb-2 h-5 w-5 text-gray-400" />
              <p className="font-medium text-gray-600 text-sm">
                Zone {index + 1}
              </p>
              <p className="text-gray-500 text-xs">Hover to add components</p>
            </div>
          </div>

          {/* Hover state - component buttons */}
          <div className="fade-in-0 slide-in-from-bottom-2 hidden h-[140px] animate-in flex-wrap items-center justify-center gap-2 p-3 duration-200 group-hover/empty-zone:flex">
            {componentTypes.map(({ type, icon: Icon }) => (
              <Button
                key={type}
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  addComponentToZone(
                    section.id,
                    zone.id,
                    type as LayoutComponent["type"],
                  );
                }}
                className="h-7 px-2 text-xs transition-colors duration-300 hover:border-blue-400 hover:bg-blue-50"
                style={{
                  animationDelay: `${
                    componentTypes.findIndex((ct) => ct.type === type) * 50
                  }ms`,
                }}
              >
                <Icon className="mr-1 h-3 w-3" />
                {type === "text"
                  ? "Text"
                  : type === "image"
                    ? "Image"
                    : type === "video"
                      ? "Video"
                      : type === "card"
                        ? "Card"
                        : type === "chart"
                          ? "Chart"
                          : type === "game"
                            ? "Game"
                            : "Quiz"}
              </Button>
            ))}
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-3">
        {!isPreviewMode && (
          <div className="flex items-center justify-start px-2">
            <Badge
              variant="outline"
              className="border-gray-300 text-gray-700 text-xs"
            >
              Zone {index + 1} ({zone.components.length} components)
            </Badge>
          </div>
        )}

        <div className="space-y-2">
          {zone.components.map((component, componentIndex) => (
            <div key={component.id}>
              {!isPreviewMode && (
                <div
                  className={`h-4 rounded-md border-2 border-dashed transition-all duration-200 ${
                    dragOverZone?.sectionId === section.id &&
                    dragOverZone?.zoneId === zone.id &&
                    dragOverZone?.index === componentIndex
                      ? "h-8 border-blue-500 bg-blue-50"
                      : "border-transparent hover:border-gray-300 hover:bg-gray-100"
                  }`}
                  onDragOver={handleDragOver}
                  onDragEnter={(e) =>
                    handleDragOverWithFeedback(
                      e,
                      section.id,
                      zone.id,
                      componentIndex,
                    )
                  }
                  onDrop={(e) =>
                    handleDrop(e, section.id, zone.id, componentIndex)
                  }
                  onDragLeave={handleDragLeave}
                >
                  {dragOverZone?.sectionId === section.id &&
                    dragOverZone?.zoneId === zone.id &&
                    dragOverZone?.index === componentIndex && (
                      <div className="flex h-full items-center justify-center font-medium text-blue-600 text-xs">
                        Drop here
                      </div>
                    )}
                </div>
              )}

              <div
                className={`${component.color} ${
                  component.size === "small"
                    ? "p-4"
                    : component.size === "large"
                      ? "p-8"
                      : "p-6"
                } ${
                  !isPreviewMode
                    ? "group rounded-lg border border-gray-200 transition-all duration-200 hover:shadow-md"
                    : "rounded-lg border border-gray-200"
                } ${
                  draggedComponent?.componentId === component.id
                    ? "scale-95 opacity-50 shadow-xl"
                    : ""
                }`}
              >
                <div className="flex items-start justify-between">
                  {!isPreviewMode && (
                    <div
                      className="mr-3 flex cursor-grab items-center opacity-60 transition-opacity active:cursor-grabbing group-hover:opacity-100"
                      draggable={true}
                      onDragStart={(e) =>
                        handleDragStart(
                          e,
                          section.id,
                          zone.id,
                          component.id,
                          componentIndex,
                        )
                      }
                      onDragEnd={handleDragEnd}
                    >
                      <GripVertical className="h-5 w-5 text-gray-500" />
                    </div>
                  )}

                  <div className="flex-1">
                    {!isPreviewMode && (
                      <div className="mb-3 flex items-center gap-2">
                        <Badge
                          variant="outline"
                          className="border-gray-300 text-gray-700 text-xs"
                        >
                          {component.type}
                        </Badge>
                        {(component.type === "text" ||
                          component.type === "card") && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleEditMode(component.id)}
                            className="h-6 px-2 text-gray-600 hover:text-gray-900"
                          >
                            <Edit3 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    )}

                    {!isPreviewMode &&
                    editingComponent === component.id &&
                    (component.type === "text" || component.type === "card") ? (
                      <div>
                        <TiptapEditor
                          content={component.content}
                          onChange={(newContent) =>
                            updateComponentContent(
                              section.id,
                              zone.id,
                              component.id,
                              newContent,
                            )
                          }
                          placeholder={`Nhập nội dung ${
                            component.type === "text" ? "văn bản" : "thẻ"
                          }...`}
                          className="mb-2"
                        />
                        <Button onClick={() => setEditingComponent(null)}>
                          Save
                        </Button>
                      </div>
                    ) : component.type === "game" ? (
                      <div className="rounded-lg border-2 border-green-200 bg-green-50 p-4">
                        <div className="mb-3 flex items-center justify-center">
                          <Gamepad2 className="mr-2 h-8 w-8 text-green-600" />
                          <div>
                            <div className="font-medium text-green-900">
                              {component.gameData?.gameType === "memory"
                                ? "Memory Game"
                                : "Puzzle Game"}
                            </div>
                            <div className="text-green-700 text-sm">
                              Interactive game component
                            </div>
                          </div>
                        </div>
                        {!isPreviewMode && (
                          <div className="flex justify-center">
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-green-300 text-green-700"
                            >
                              Configure Game
                            </Button>
                          </div>
                        )}
                      </div>
                    ) : component.type === "quiz" ? (
                      <div className="rounded-lg border-2 border-purple-200 bg-purple-50 p-4">
                        <div className="mb-3 flex items-center justify-center">
                          <HelpCircle className="mr-2 h-8 w-8 text-purple-600" />
                          <div>
                            <div className="font-medium text-purple-900">
                              {component.quizData?.quizType ===
                              "multiple-choice"
                                ? "Multiple Choice Quiz"
                                : "True/False Quiz"}
                            </div>
                            <div className="text-purple-700 text-sm">
                              Interactive quiz component
                            </div>
                          </div>
                        </div>
                        {!isPreviewMode && (
                          <div className="flex justify-center">
                            <Button
                              size="sm"
                              variant="outline"
                              className="border-purple-300 text-purple-700"
                            >
                              Configure Quiz
                            </Button>
                          </div>
                        )}
                      </div>
                    ) : (
                      <div
                        className={`${
                          component.type === "text"
                            ? "font-serif text-base leading-relaxed"
                            : component.type === "card"
                              ? "font-sans"
                              : "font-sans"
                        }`}
                        dangerouslySetInnerHTML={{ __html: component.content }}
                      />
                    )}
                  </div>

                  {!isPreviewMode && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-gray-500 opacity-0 transition-opacity hover:text-red-600 group-hover:opacity-100"
                      onClick={() =>
                        confirmDeleteComponent(
                          section.id,
                          zone.id,
                          component.id,
                        )
                      }
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              {!isPreviewMode &&
                componentIndex === zone.components.length - 1 && (
                  <div
                    className={`h-4 rounded-md border-2 border-dashed transition-all duration-200 ${
                      dragOverZone?.sectionId === section.id &&
                      dragOverZone?.zoneId === zone.id &&
                      dragOverZone?.index === componentIndex + 1
                        ? "h-8 border-blue-500 bg-blue-50"
                        : "border-transparent hover:border-gray-300 hover:bg-gray-100"
                    }`}
                    onDragOver={handleDragOver}
                    onDragEnter={(e) =>
                      handleDragOverWithFeedback(
                        e,
                        section.id,
                        zone.id,
                        componentIndex + 1,
                      )
                    }
                    onDrop={(e) =>
                      handleDrop(e, section.id, zone.id, componentIndex + 1)
                    }
                    onDragLeave={handleDragLeave}
                  >
                    {dragOverZone?.sectionId === section.id &&
                      dragOverZone?.zoneId === zone.id &&
                      dragOverZone?.index === componentIndex + 1 && (
                        <div className="flex h-full items-center justify-center font-medium text-blue-600 text-xs">
                          Drop here
                        </div>
                      )}
                  </div>
                )}
            </div>
          ))}
        </div>

        {/* Add component area at bottom of zone */}
        {!isPreviewMode && (
          <div className="group/add-area relative mt-4 min-h-[80px] rounded-lg border-2 border-gray-200 border-dashed bg-gray-50/30 transition-all duration-300 hover:border-blue-300 hover:bg-blue-50/50">
            {/* Default state - shows on no hover */}
            <div className="flex min-h-[80px] items-center justify-center transition-opacity duration-200 group-hover/add-area:hidden">
              <div className="flex items-center gap-2 text-gray-600 opacity-60">
                <Plus className="h-4 w-4" />
                <span className="font-medium text-sm">Add Component</span>
              </div>
            </div>

            {/* Hover state - shows component buttons inline */}
            <div className="fade-in-0 slide-in-from-bottom-2 hidden min-h-[80px] animate-in flex-wrap items-center justify-center gap-2 p-4 duration-200 group-hover/add-area:flex">
              {componentTypes.map(({ type, icon: Icon }, buttonIndex) => (
                <Button
                  key={type}
                  variant="outline"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation();
                    addComponentToZone(
                      section.id,
                      zone.id,
                      type as LayoutComponent["type"],
                    );
                  }}
                  className="h-7 px-2 text-xs transition-colors duration-300 hover:border-blue-400 hover:bg-blue-50"
                  style={{ animationDelay: `${buttonIndex * 50}ms` }}
                >
                  <Icon className="mr-1 h-3 w-3" />
                  {type === "text"
                    ? "Text"
                    : type === "image"
                      ? "Image"
                      : type === "video"
                        ? "Video"
                        : type === "card"
                          ? "Card"
                          : type === "chart"
                            ? "Chart"
                            : type === "game"
                              ? "Game"
                              : "Quiz"}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }
}
