import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Undo } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import type { Lesson } from "../types";

interface LessonHeaderProps {
  lesson: Lesson;
  isPreviewMode: boolean;
  canUndo: boolean;
  canRedo: boolean;
  onUndo: () => void;
  onRedo: () => void;
  onTogglePreview: () => void;
  onSave: () => void;
}

export const LessonHeader = ({
  lesson,
  isPreviewMode,
  canUndo,
  canRedo,
  onUndo,
  onRedo,
  onTogglePreview,
  onSave,
}: LessonHeaderProps) => {
  return (
    <header className="fixed top-0 right-0 left-0 z-20 border-b bg-white shadow-sm">
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <div className="mb-2 flex items-center gap-3">
            <BookOpen className="h-6 w-6 text-blue-600" />
            <h1 className="font-bold font-sans text-2xl text-gray-900">
              {lesson.title}
            </h1>
          </div>
          <p className="font-serif text-gray-600 text-sm">
            {lesson.description}
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={onUndo}
            disabled={!canUndo}
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onRedo}
            disabled={!canRedo}
          >
            <Redo className="h-4 w-4" />
          </Button>
          <Separator orientation="vertical" className="h-6" />
          <Button
            variant={isPreviewMode ? "default" : "outline"}
            size="sm"
            onClick={onTogglePreview}
          >
            <Eye className="mr-2 h-4 w-4" />
            {isPreviewMode ? "Edit" : "Preview"}
          </Button>
          <Button size="sm" onClick={onSave}>
            <Save className="mr-2 h-4 w-4" />
            Save Lesson
          </Button>
        </div>
      </div>
    </header>
  );
};
