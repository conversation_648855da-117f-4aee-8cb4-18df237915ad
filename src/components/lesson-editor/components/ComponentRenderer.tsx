import {
  Edit3,
  Gamepad2,
  GripVertical,
  HelpCircle,
  Trash2,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { LayoutComponent } from "../types";
import BeautyEditor from "../tiptap-editor";

interface ComponentRendererProps {
  component: LayoutComponent;
  componentIndex: number;
  sectionId: string;
  zoneId: string;
  isPreviewMode: boolean;
  isEditing: boolean;
  isDragged: boolean;
  onDeleteComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string
  ) => void;
  onUpdateComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string,
    content: string
  ) => void;
  onToggleEdit: (componentId: string) => void;
  onDragStart: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    componentId: string,
    index: number
  ) => void;
  onDragEnd: () => void;
}

export const ComponentRenderer = ({
  component,
  componentIndex,
  sectionId,
  zoneId,
  isPreviewMode,
  isEditing,
  isDragged,
  onDeleteComponent,
  onUpdateComponent,
  onToggleEdit,
  onDragStart,
  onDragEnd,
}: ComponentRendererProps) => {
  const renderComponentContent = () => {
    if (
      !isPreviewMode &&
      isEditing &&
      (component.type === "text" || component.type === "card")
    ) {
      // Ensure we have valid JSON content for BeautyEditor
      let validJsonContent = "{}";
      try {
        if (component.content && typeof component.content === "string") {
          JSON.parse(component.content); // Validate JSON
          validJsonContent = component.content;
        }
      } catch (error) {
        // If content is not valid JSON, create a default JSON structure
        validJsonContent = JSON.stringify({
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text:
                    typeof component.content === "string"
                      ? component.content
                      : "Enter your content here...",
                },
              ],
            },
          ],
        });
      }

      return (
        <div>
          <BeautyEditor
            content={validJsonContent}
            setContent={(content: string) =>
              onUpdateComponent(sectionId, zoneId, component.id, content)
            }
            contentId={component.id}
          />
          <Button
            onClick={() => onToggleEdit(component.id)}
            className="mt-2"
            size="sm"
          >
            Save
          </Button>
        </div>
      );
    }

    if (component.type === "game") {
      return (
        <div className="rounded-lg border-2 border-green-200 bg-green-50 p-4">
          <div className="mb-3 flex items-center justify-center">
            <Gamepad2 className="mr-2 h-8 w-8 text-green-600" />
            <div>
              <div className="font-medium text-green-900">
                {component.gameData?.gameType === "memory"
                  ? "Memory Game"
                  : "Puzzle Game"}
              </div>
              <div className="text-green-700 text-sm">
                Interactive game component
              </div>
            </div>
          </div>
          {!isPreviewMode && (
            <div className="flex justify-center">
              <Button
                size="sm"
                variant="outline"
                className="border-green-300 text-green-700"
              >
                Configure Game
              </Button>
            </div>
          )}
        </div>
      );
    }

    if (component.type === "quiz") {
      return (
        <div className="rounded-lg border-2 border-purple-200 bg-purple-50 p-4">
          <div className="mb-3 flex items-center justify-center">
            <HelpCircle className="mr-2 h-8 w-8 text-purple-600" />
            <div>
              <div className="font-medium text-purple-900">
                {component.quizData?.quizType === "multiple-choice"
                  ? "Multiple Choice Quiz"
                  : "Quiz"}
              </div>
              <div className="text-purple-700 text-sm">
                Interactive quiz component
              </div>
            </div>
          </div>
          {!isPreviewMode && (
            <div className="flex justify-center">
              <Button
                size="sm"
                variant="outline"
                className="border-purple-300 text-purple-700"
              >
                Configure Quiz
              </Button>
            </div>
          )}
        </div>
      );
    }

    if (component.type === "image") {
      return (
        <div className="rounded-lg border-2 border-gray-200 bg-gray-50 p-8 text-center">
          <div className="text-gray-600">📷</div>
          <div className="mt-2 font-medium text-gray-700">
            {component.content}
          </div>
          {!isPreviewMode && (
            <Button size="sm" variant="outline" className="mt-3">
              Upload Image
            </Button>
          )}
        </div>
      );
    }

    if (component.type === "video") {
      return (
        <div className="rounded-lg border-2 border-gray-200 bg-gray-50 p-8 text-center">
          <div className="text-gray-600">🎥</div>
          <div className="mt-2 font-medium text-gray-700">
            {component.content}
          </div>
          {!isPreviewMode && (
            <Button size="sm" variant="outline" className="mt-3">
              Embed Video
            </Button>
          )}
        </div>
      );
    }

    // Default text/card content - render JSON content from TiptapEditor
    if (component.type === "text" || component.type === "card") {
      // Ensure we have valid JSON content for BeautyEditor
      let validJsonContent = "{}";
      try {
        if (component.content && typeof component.content === "string") {
          JSON.parse(component.content); // Validate JSON
          validJsonContent = component.content;
        }
      } catch (error) {
        // If content is not valid JSON, create a default JSON structure
        validJsonContent = JSON.stringify({
          type: "doc",
          content: [
            {
              type: "paragraph",
              content: [
                {
                  type: "text",
                  text:
                    typeof component.content === "string"
                      ? component.content
                      : "No content",
                },
              ],
            },
          ],
        });
      }

      return (
        <BeautyEditor
          content={validJsonContent}
          setContent={() => {}} // Read-only in preview mode
          contentId={`preview-${component.id}`}
        />
      );
    }

    // For other component types, display as plain text
    return (
      <div className="prose prose-sm max-w-none">
        <p>{component.content}</p>
      </div>
    );
  };

  return (
    <div
      className={`${component.color} ${
        component.size === "small"
          ? "p-4"
          : component.size === "large"
          ? "p-8"
          : "p-6"
      } ${
        !isPreviewMode
          ? "group rounded-lg border border-gray-200 transition-all duration-200 hover:shadow-md"
          : "rounded-lg border border-gray-200"
      } ${isDragged ? "scale-95 opacity-50 shadow-xl" : ""}`}
    >
      <div className="flex items-start justify-between">
        {!isPreviewMode && (
          <div
            className="mr-3 flex cursor-grab items-center opacity-60 transition-opacity active:cursor-grabbing group-hover:opacity-100"
            draggable={true}
            onDragStart={(e) =>
              onDragStart(e, sectionId, zoneId, component.id, componentIndex)
            }
            onDragEnd={onDragEnd}
          >
            <GripVertical className="h-5 w-5 text-gray-500" />
          </div>
        )}

        <div className="flex-1">
          {!isPreviewMode && (
            <div className="mb-3 flex items-center gap-2">
              <Badge
                variant="outline"
                className="border-gray-300 text-gray-700 text-xs"
              >
                {component.type}
              </Badge>
              {(component.type === "text" || component.type === "card") && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onToggleEdit(component.id)}
                  className="h-6 px-2 text-gray-600 hover:text-gray-900"
                >
                  <Edit3 className="h-3 w-3" />
                </Button>
              )}
            </div>
          )}

          {renderComponentContent()}
        </div>

        {!isPreviewMode && (
          <div className="ml-3 flex items-start gap-1 opacity-0 transition-opacity group-hover:opacity-100">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteComponent(sectionId, zoneId, component.id)}
              className="h-6 w-6 p-0 text-red-600 hover:bg-red-50 hover:text-red-800"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
