import { LayoutGrid, Trash2 } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { LayoutSection } from "../types";
import { ZoneRenderer } from "./ZoneRenderer";

interface SectionRendererProps {
  section: LayoutSection;
  sectionIndex: number;
  isLastSection: boolean;
  isPreviewMode: boolean;
  onDeleteSection: (sectionId: string) => void;
  onZoneClick: (
    event: React.MouseEvent,
    sectionId: string,
    zoneId: string,
  ) => void;
  onAddComponent: (sectionId: string, zoneId: string, type: any) => void;
  onDeleteComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string,
  ) => void;
  onUpdateComponent: (
    sectionId: string,
    zoneId: string,
    componentId: string,
    content: string,
  ) => void;
  onToggleEdit: (componentId: string) => void;
  editingComponent: string | null;
  draggedComponent: any;
  dragOverZone: any;
  onDragStart: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    componentId: string,
    index: number,
  ) => void;
  onDragOver: (e: React.DragEvent) => void;
  onDragOverWithFeedback: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    index: number,
  ) => void;
  onDrop: (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    index: number,
  ) => void;
  onDragEnd: () => void;
  onDragLeave: (e: React.DragEvent) => void;
}

export const SectionRenderer = ({
  section,
  sectionIndex,
  isLastSection,
  isPreviewMode,
  onDeleteSection,
  onZoneClick,
  onAddComponent,
  onDeleteComponent,
  onUpdateComponent,
  onToggleEdit,
  editingComponent,
  draggedComponent,
  dragOverZone,
  onDragStart,
  onDragOver,
  onDragOverWithFeedback,
  onDrop,
  onDragEnd,
  onDragLeave,
}: SectionRendererProps) => {
  const borderClass = isLastSection ? "rounded-b-lg border-b" : "border-b-0";

  return (
    <div className="group relative">
      {!isPreviewMode && (
        <div className="mb-3 flex items-center justify-between rounded-t-lg border-gray-200 border-b bg-gray-50/80 px-4 py-2">
          <Badge
            variant="outline"
            className="border-blue-200 bg-blue-50 text-blue-800"
          >
            <LayoutGrid className="mr-1 inline h-3 w-3" />
            {section.name}
          </Badge>
          <div className="flex items-center gap-2 opacity-0 transition-opacity group-hover:opacity-100">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteSection(section.id)}
              className="text-red-600 hover:bg-red-50 hover:text-red-800"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      <div
        className={
          section.template.id === "hero-two"
            ? `space-y-6 border-gray-200 border-r border-l bg-white p-6 ${borderClass} ${
                sectionIndex === 0 ? "rounded-t-lg border-t" : ""
              }`
            : `${
                section.template.gridClass
              } border-gray-200 border-r border-l bg-white p-6 ${borderClass} ${
                sectionIndex === 0 ? "rounded-t-lg border-t" : ""
              }`
        }
      >
        {section.template.id === "hero-two" ? (
          <>
            <div className="col-span-2">
              <ZoneRenderer
                section={section}
                zone={section.zones[0]}
                zoneIndex={0}
                isPreviewMode={isPreviewMode}
                onZoneClick={onZoneClick}
                onAddComponent={onAddComponent}
                onDeleteComponent={onDeleteComponent}
                onUpdateComponent={onUpdateComponent}
                onToggleEdit={onToggleEdit}
                editingComponent={editingComponent}
                draggedComponent={draggedComponent}
                dragOverZone={dragOverZone}
                onDragStart={onDragStart}
                onDragOver={onDragOver}
                onDragOverWithFeedback={onDragOverWithFeedback}
                onDrop={onDrop}
                onDragEnd={onDragEnd}
                onDragLeave={onDragLeave}
              />
            </div>
            <div className="grid grid-cols-2 gap-6">
              {section.zones.slice(1).map((zone, index) => (
                <div key={zone.id}>
                  <ZoneRenderer
                    section={section}
                    zone={zone}
                    zoneIndex={index + 1}
                    isPreviewMode={isPreviewMode}
                    onZoneClick={onZoneClick}
                    onAddComponent={onAddComponent}
                    onDeleteComponent={onDeleteComponent}
                    onUpdateComponent={onUpdateComponent}
                    onToggleEdit={onToggleEdit}
                    editingComponent={editingComponent}
                    draggedComponent={draggedComponent}
                    dragOverZone={dragOverZone}
                    onDragStart={onDragStart}
                    onDragOver={onDragOver}
                    onDragOverWithFeedback={onDragOverWithFeedback}
                    onDrop={onDrop}
                    onDragEnd={onDragEnd}
                    onDragLeave={onDragLeave}
                  />
                </div>
              ))}
            </div>
          </>
        ) : (
          section.zones.map((zone, index) => (
            <div key={zone.id}>
              <ZoneRenderer
                section={section}
                zone={zone}
                zoneIndex={index}
                isPreviewMode={isPreviewMode}
                onZoneClick={onZoneClick}
                onAddComponent={onAddComponent}
                onDeleteComponent={onDeleteComponent}
                onUpdateComponent={onUpdateComponent}
                onToggleEdit={onToggleEdit}
                editingComponent={editingComponent}
                draggedComponent={draggedComponent}
                dragOverZone={dragOverZone}
                onDragStart={onDragStart}
                onDragOver={onDragOver}
                onDragOverWithFeedback={onDragOverWithFeedback}
                onDrop={onDrop}
                onDragEnd={onDragEnd}
                onDragLeave={onDragLeave}
              />
            </div>
          ))
        )}
      </div>
    </div>
  );
};
