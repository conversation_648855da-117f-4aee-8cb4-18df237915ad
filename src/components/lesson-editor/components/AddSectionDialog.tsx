import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import type { LayoutTemplate } from "../types";
import { layoutTemplates } from "../constants";

interface AddSectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAddSection: (template: LayoutTemplate) => void;
}

export const AddSectionDialog = ({
  isOpen,
  onClose,
  onAddSection,
}: AddSectionDialogProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-xl">
        <DialogHeader>
          <DialogTitle>Add Layout Section</DialogTitle>
          <DialogDescription>
            Choose a layout template to add as a new section to your current
            slide.
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4 grid grid-cols-2 gap-3">
          {layoutTemplates.map((template) => {
            const IconComponent = template.icon;
            return (
              <Button
                key={template.id}
                onClick={() => {
                  onAddSection(template);
                  onClose();
                }}
                className="h-auto p-4"
                variant="outline"
              >
                <div className="text-center">
                  <IconComponent className="mx-auto mb-2 h-8 w-8 text-gray-600" />
                  <div className="font-medium text-sm">{template.name}</div>
                  <div className="mt-1 text-muted-foreground text-xs">
                    {template.description}
                  </div>
                </div>
              </Button>
            );
          })}
        </div>

        <div className="mt-6 flex gap-2">
          <Button
            variant="ghost"
            onClick={onClose}
            className="flex-1"
          >
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
