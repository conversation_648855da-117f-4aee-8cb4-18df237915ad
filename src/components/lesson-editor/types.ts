export interface LayoutComponent {
  id: string;
  type: "text" | "image" | "video" | "card" | "grid" | "game" | "quiz";
  content: string;
  color?: string;
  size?: "small" | "medium" | "large";
  gameData?: any;
  quizData?: any;
}

export interface LayoutZone {
  id: string;
  components: LayoutComponent[];
}

export interface LayoutTemplate {
  id: string;
  name: string;
  icon: any;
  description: string;
  zones: LayoutZone[];
  gridClass: string;
}

export interface LayoutSection {
  id: string;
  name: string;
  template: LayoutTemplate;
  zones: LayoutZone[];
}

export interface Slide {
  id: string;
  name: string;
  sections: LayoutSection[];
  thumbnail?: string;
}

export interface Lesson {
  id: string;
  title: string;
  description: string;
  slides: Slide[];
}

export interface DraggedComponent {
  sectionId: string;
  zoneId: string;
  componentId: string;
  index: number;
}

export interface DragOverZone {
  sectionId: string;
  zoneId: string;
  index: number;
}

export interface SelectedZone {
  sectionId: string;
  zoneId: string;
}

export interface DeleteConfirm {
  type: "component" | "section" | "slide";
  data: any;
  title: string;
  description: string;
}

export interface PickerPosition {
  x: number;
  y: number;
}

export interface ComponentType {
  type: string;
  icon: any;
  label: string;
  color: string;
}
