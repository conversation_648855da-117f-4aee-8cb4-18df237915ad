import { useState } from "react";
import type { DraggedComponent, DragOverZone, LayoutComponent, Slide } from "../types";

export const useDragAndDrop = () => {
  const [draggedComponent, setDraggedComponent] = useState<DraggedComponent | null>(null);
  const [dragOverZone, setDragOverZone] = useState<DragOverZone | null>(null);

  const handleDragStart = (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    componentId: string,
    index: number
  ) => {
    e.stopPropagation();
    setDraggedComponent({ sectionId, zoneId, componentId, index });
    e.dataTransfer.effectAllowed = "move";

    // Create custom drag image
    const dragImage = document.createElement("div");
    dragImage.innerHTML = `
      <div style="
        background: white; 
        border: 2px solid #e5e7eb; 
        border-radius: 8px; 
        padding: 8px 12px; 
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 12px;
        color: #6b7280;
      ">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <circle cx="9" cy="12" r="1"/>
          <circle cx="9" cy="5" r="1"/>
          <circle cx="9" cy="19" r="1"/>
          <circle cx="15" cy="12" r="1"/>
          <circle cx="15" cy="5" r="1"/>
          <circle cx="15" cy="19" r="1"/>
        </svg>
        Moving component...
      </div>
    `;
    dragImage.style.position = "absolute";
    dragImage.style.top = "-1000px";
    document.body.appendChild(dragImage);

    e.dataTransfer.setDragImage(dragImage, 75, 20);

    setTimeout(() => {
      document.body.removeChild(dragImage);
    }, 0);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = "move";
  };

  const handleDragOverWithFeedback = (
    e: React.DragEvent,
    sectionId: string,
    zoneId: string,
    index: number
  ) => {
    e.preventDefault();
    e.stopPropagation();
    e.dataTransfer.dropEffect = "move";
    setDragOverZone({ sectionId, zoneId, index });
  };

  const handleDrop = (
    e: React.DragEvent,
    targetSectionId: string,
    targetZoneId: string,
    targetIndex: number,
    currentSlide: Slide
  ): Slide | null => {
    e.preventDefault();
    e.stopPropagation();
    setDragOverZone(null);

    if (!draggedComponent) {
      return null;
    }

    const {
      sectionId: sourceSectionId,
      zoneId: sourceZoneId,
      componentId,
      index: sourceIndex,
    } = draggedComponent;

    if (
      sourceSectionId === targetSectionId &&
      sourceZoneId === targetZoneId &&
      sourceIndex === targetIndex
    ) {
      setDraggedComponent(null);
      return null;
    }

    // Create a copy of the slide
    let updatedSlide = { ...currentSlide };
    let movedComponent: LayoutComponent | null = null;

    // Remove component from source
    updatedSlide.sections = updatedSlide.sections.map((section) => {
      if (section.id === sourceSectionId) {
        return {
          ...section,
          zones: section.zones.map((zone) => {
            if (zone.id === sourceZoneId) {
              const newComponents = [...zone.components];
              [movedComponent] = newComponents.splice(sourceIndex, 1);
              return { ...zone, components: newComponents };
            }
            return zone;
          }),
        };
      }
      return section;
    });

    // Add component to target
    if (movedComponent) {
      updatedSlide.sections = updatedSlide.sections.map((section) => {
        if (section.id === targetSectionId) {
          return {
            ...section,
            zones: section.zones.map((zone) => {
              if (zone.id === targetZoneId) {
                const newComponents = [...zone.components];
                // Adjust index if moving within same zone
                const adjustedIndex =
                  sourceSectionId === targetSectionId &&
                  sourceZoneId === targetZoneId &&
                  sourceIndex < targetIndex
                    ? targetIndex - 1
                    : targetIndex;
                newComponents.splice(adjustedIndex, 0, movedComponent!);
                return { ...zone, components: newComponents };
              }
              return zone;
            }),
          };
        }
        return section;
      });
    }

    setDraggedComponent(null);
    return updatedSlide;
  };

  const handleDragEnd = () => {
    setDraggedComponent(null);
    setDragOverZone(null);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    if (!e.currentTarget.contains(e.relatedTarget as Node)) {
      setDragOverZone(null);
    }
  };

  return {
    draggedComponent,
    dragOverZone,
    handleDragStart,
    handleDragOver,
    handleDragOverWithFeedback,
    handleDrop,
    handleDragEnd,
    handleDragLeave,
  };
};
