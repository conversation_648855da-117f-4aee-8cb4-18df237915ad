import { useState } from "react";
import type { Lesson } from "../types";

export const useHistory = () => {
  const [history, setHistory] = useState<Lesson[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);

  const saveToHistory = (lesson: Lesson) => {
    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push({ ...lesson });
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  };

  const undo = (): Lesson | null => {
    if (historyIndex > 0) {
      setHistoryIndex(historyIndex - 1);
      return { ...history[historyIndex - 1] };
    }
    return null;
  };

  const redo = (): Lesson | null => {
    if (historyIndex < history.length - 1) {
      setHistoryIndex(historyIndex + 1);
      return { ...history[historyIndex + 1] };
    }
    return null;
  };

  const canUndo = historyIndex > 0;
  const canRedo = historyIndex < history.length - 1;

  return {
    saveToHistory,
    undo,
    redo,
    canUndo,
    canRedo,
  };
};
