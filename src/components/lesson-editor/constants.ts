import {
  <PERSON><PERSON><PERSON>,
  Columns,
  Edit3,
  Eye,
  FileText,
  Gamepad2,
  Grid,
  GripVertical,
  HelpCircle,
  ImageIcon,
  LayoutGrid,
  Plus,
  Redo,
  Rows,
  Save,
  Square,
  Trash2,
  Type,
  Undo,
  Video,
} from "lucide-react";
import type { ComponentType, LayoutTemplate } from "./types";

export const layoutTemplates: LayoutTemplate[] = [
  {
    id: "single",
    name: "Single Column",
    icon: Square,
    description: "One full-width content area",
    zones: [{ id: "zone-1", components: [] }],
    gridClass: "grid grid-cols-1 gap-6",
  },
  {
    id: "two-column",
    name: "Two Columns",
    icon: Columns,
    description: "Side-by-side layout",
    zones: [
      { id: "zone-1", components: [] },
      { id: "zone-2", components: [] },
    ],
    gridClass: "grid grid-cols-2 gap-6",
  },
  {
    id: "three-column",
    name: "Three Columns",
    icon: Grid,
    description: "Triple column layout",
    zones: [
      { id: "zone-1", components: [] },
      { id: "zone-2", components: [] },
      { id: "zone-3", components: [] },
    ],
    gridClass: "grid grid-cols-3 gap-6",
  },
  {
    id: "hero-two",
    name: "Hero + Two Columns",
    icon: Rows,
    description: "Full-width header with two columns below",
    zones: [
      { id: "zone-1", components: [] },
      { id: "zone-2", components: [] },
      { id: "zone-3", components: [] },
    ],
    gridClass: "grid grid-cols-2 gap-6",
  },
  {
    id: "grid-four",
    name: "Four Grid",
    icon: LayoutGrid,
    description: "2x2 grid layout",
    zones: [
      { id: "zone-1", components: [] },
      { id: "zone-2", components: [] },
      { id: "zone-3", components: [] },
      { id: "zone-4", components: [] },
    ],
    gridClass: "grid grid-cols-2 gap-6",
  },
];

export const componentTypes: ComponentType[] = [
  {
    type: "text",
    icon: Type,
    label: "Text Block",
    color: "bg-blue-100 text-blue-800",
  },
  {
    type: "image",
    icon: ImageIcon,
    label: "Image",
    color: "bg-green-100 text-green-800",
  },
  {
    type: "video",
    icon: Video,
    label: "Video",
    color: "bg-purple-100 text-purple-800",
  },
  {
    type: "card",
    icon: FileText,
    label: "Content Card",
    color: "bg-orange-100 text-orange-800",
  },
  {
    type: "chart",
    icon: LayoutGrid,
    label: "Chart",
    color: "bg-teal-100 text-teal-800",
  },
  {
    type: "game",
    icon: Gamepad2,
    label: "Game",
    color: "bg-green-100 text-green-800",
  },
  {
    type: "quiz",
    icon: HelpCircle,
    label: "Quiz",
    color: "bg-purple-100 text-purple-800",
  },
];

export const COMPONENT_COLORS = [
  "bg-yellow-100",
  "bg-green-100",
  "bg-purple-100",
  "bg-blue-100",
  "bg-orange-100",
  "bg-teal-100",
];

export const DEFAULT_CONTENT_MAP = {
  text: JSON.stringify({
    type: "doc",
    content: [
      {
        type: "paragraph",
        content: [
          {
            type: "text",
            text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit.",
          },
        ],
      },
    ],
  }),
  card: JSON.stringify({
    type: "doc",
    content: [
      {
        type: "paragraph",
        content: [
          {
            type: "text",
            text: "Content card with customizable information",
          },
        ],
      },
    ],
  }),
  image: "Image placeholder - Click to upload",
  video: "Video placeholder - Click to embed",
  grid: "Grid layout for organizing multiple elements",
  game: "Game component - Click to configure",
  quiz: "Quiz component - Click to configure",
  default: JSON.stringify({
    type: "doc",
    content: [
      {
        type: "paragraph",
        content: [
          {
            type: "text",
            text: "Default content",
          },
        ],
      },
    ],
  }),
};

export const COMPONENT_DESCRIPTIONS = {
  text: "Rich text content",
  image: "Images and media",
  video: "Video embeds",
  card: "Content cards",
  chart: "Charts and graphs",
  game: "Interactive games",
  quiz: "Quiz and assessments",
};
