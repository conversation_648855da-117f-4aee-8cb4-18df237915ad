import {
  COMPONENT_COLORS,
  DEFAULT_CONTENT_MAP,
  layoutTemplates,
} from "./constants";
import type { LayoutComponent, LayoutSection, Lesson, Slide } from "./types";

// Generate stable IDs for SSR compatibility
let idCounter = 0;
export const generateId = (prefix: string) => `${prefix}-${++idCounter}`;

// Color counter for consistent color selection
let colorCounter = 0;

export const getRandomColor = () => {
  return COMPONENT_COLORS[colorCounter++ % COMPONENT_COLORS.length];
};

export const getDefaultContent = (type: string) => {
  return (
    DEFAULT_CONTENT_MAP[type as keyof typeof DEFAULT_CONTENT_MAP] ||
    DEFAULT_CONTENT_MAP.default
  );
};

export const createDefaultSlide = (slideNumber: number): Slide => {
  const defaultTemplate = layoutTemplates.find((t) => t.id === "single")!;
  const defaultSection: LayoutSection = {
    id: generateId("section"),
    name: "Single Column Section",
    template: defaultTemplate,
    zones: [
      {
        id: generateId("zone"),
        components: [
          {
            id: generateId("text"),
            type: "text",
            content:
              "<h2>Welcome to your lesson</h2><p>Start editing this content to create your lesson material.</p>",
            color: "bg-white",
            size: "medium",
          },
        ],
      },
    ],
  };

  return {
    id: generateId("slide"),
    name: `Slide ${slideNumber}`,
    sections: [defaultSection],
  };
};

export const createNewComponent = (
  type: LayoutComponent["type"],
): LayoutComponent => {
  const newComponent: LayoutComponent = {
    id: generateId(type),
    type,
    content: getDefaultContent(type),
    color: getRandomColor(),
    size: "medium",
  };

  if (type === "game") {
    newComponent.gameData = { gameType: "memory", config: {} };
  } else if (type === "quiz") {
    newComponent.quizData = {
      quizType: "multiple-choice",
      questions: [],
      config: {},
    };
  }

  return newComponent;
};

export const calculateSmartPickerPosition = (
  rect: DOMRect,
  pickerWidth = 250,
  pickerHeight = 350,
) => {
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;

  // Smart positioning logic
  let x = rect.right + 15;
  let y = rect.top;

  // If picker would go off right edge, position on left side
  if (x + pickerWidth > viewportWidth) {
    x = rect.left - pickerWidth - 15;
  }

  // If picker would go off bottom, adjust y position
  if (y + pickerHeight > viewportHeight) {
    y = viewportHeight - pickerHeight - 20;
  }

  // Ensure picker doesn't go above viewport
  if (y < 20) {
    y = 20;
  }

  return { x, y };
};

export const countComponentsInSlide = (slide: Slide): number => {
  return slide.sections.reduce(
    (total, section) =>
      total +
      section.zones.reduce(
        (zoneTotal, zone) => zoneTotal + zone.components.length,
        0,
      ),
    0,
  );
};

export const countComponentsInSection = (section: LayoutSection): number => {
  return section.zones.reduce(
    (total, zone) => total + zone.components.length,
    0,
  );
};
