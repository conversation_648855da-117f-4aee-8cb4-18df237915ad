import {
  COMPONENT_COLORS,
  DEFAULT_CONTENT_MAP,
  layoutTemplates,
} from "./constants";
import type { LayoutComponent, LayoutSection, Slide } from "./types";

// Generate stable IDs for SSR compatibility
let idCounter = 0;
export const generateId = (prefix: string) => `${prefix}-${++idCounter}`;

// Color counter for consistent color selection
let colorCounter = 0;

export const getRandomColor = () => {
  return COMPONENT_COLORS[colorCounter++ % COMPONENT_COLORS.length];
};

export const getDefaultContent = (type: string) => {
  return (
    DEFAULT_CONTENT_MAP[type as keyof typeof DEFAULT_CONTENT_MAP] ||
    DEFAULT_CONTENT_MAP.default
  );
};

export const createDefaultSlide = (slideNumber: number): Slide => {
  const defaultTemplate = layoutTemplates.find((t) => t.id === "single")!;
  const defaultSection: LayoutSection = {
    id: generateId("section"),
    name: "Single Column Section",
    template: defaultTemplate,
    zones: [
      {
        id: generateId("zone"),
        components: [
          {
            id: generateId("text"),
            type: "text",
            content: JSON.stringify({
              type: "doc",
              content: [
                {
                  type: "heading",
                  attrs: { level: 2 },
                  content: [
                    {
                      type: "text",
                      text: "Welcome to your lesson",
                    },
                  ],
                },
                {
                  type: "paragraph",
                  content: [
                    {
                      type: "text",
                      text: "Start editing this content to create your lesson material.",
                    },
                  ],
                },
              ],
            }),
            color: "bg-white",
            size: "medium",
          },
        ],
      },
    ],
  };

  return {
    id: generateId("slide"),
    name: `Slide ${slideNumber}`,
    sections: [defaultSection],
  };
};

export const createNewComponent = (
  type: LayoutComponent["type"]
): LayoutComponent => {
  const newComponent: LayoutComponent = {
    id: generateId(type),
    type,
    content: getDefaultContent(type),
    color: getRandomColor(),
    size: "medium",
  };

  if (type === "game") {
    newComponent.gameData = { gameType: "memory", config: {} };
  } else if (type === "quiz") {
    newComponent.quizData = {
      quizType: "multiple-choice",
      questions: [],
      config: {},
    };
  }

  return newComponent;
};

export const calculateSmartPickerPosition = (
  rect: DOMRect,
  pickerWidth = 250,
  pickerHeight = 350
) => {
  const viewportHeight = window.innerHeight;
  const viewportWidth = window.innerWidth;

  // Smart positioning logic
  let x = rect.right + 15;
  let y = rect.top;

  // If picker would go off right edge, position on left side
  if (x + pickerWidth > viewportWidth) {
    x = rect.left - pickerWidth - 15;
  }

  // If picker would go off bottom, adjust y position
  if (y + pickerHeight > viewportHeight) {
    y = viewportHeight - pickerHeight - 20;
  }

  // Ensure picker doesn't go above viewport
  if (y < 20) {
    y = 20;
  }

  return { x, y };
};

export const countComponentsInSlide = (slide: Slide): number => {
  return slide.sections.reduce(
    (total, section) =>
      total +
      section.zones.reduce(
        (zoneTotal, zone) => zoneTotal + zone.components.length,
        0
      ),
    0
  );
};

export const countComponentsInSection = (section: LayoutSection): number => {
  return section.zones.reduce(
    (total, zone) => total + zone.components.length,
    0
  );
};

// Convert TiptapEditor JSON content to HTML for display
export const convertTiptapJsonToHtml = (jsonContent: unknown): string => {
  if (
    !jsonContent ||
    typeof jsonContent !== "object" ||
    !("content" in jsonContent)
  ) {
    return "";
  }

  const convertNode = (node: unknown): string => {
    if (!node || typeof node !== "object") return "";

    const nodeObj = node as Record<string, unknown>;

    switch (nodeObj.type) {
      case "doc": {
        const content = Array.isArray(nodeObj.content) ? nodeObj.content : [];
        return content.map(convertNode).join("");
      }

      case "paragraph": {
        const content = Array.isArray(nodeObj.content) ? nodeObj.content : [];
        const pContent = content.map(convertNode).join("");
        return `<p>${pContent}</p>`;
      }

      case "heading": {
        const attrs = (nodeObj.attrs as Record<string, unknown>) || {};
        const level = typeof attrs.level === "number" ? attrs.level : 1;
        const content = Array.isArray(nodeObj.content) ? nodeObj.content : [];
        const hContent = content.map(convertNode).join("");
        return `<h${level}>${hContent}</h${level}>`;
      }

      case "text": {
        let text = typeof nodeObj.text === "string" ? nodeObj.text : "";

        // Apply marks (formatting)
        if (Array.isArray(nodeObj.marks)) {
          for (const mark of nodeObj.marks) {
            if (typeof mark === "object" && mark && "type" in mark) {
              const markObj = mark as Record<string, unknown>;
              switch (markObj.type) {
                case "bold":
                  text = `<strong>${text}</strong>`;
                  break;
                case "italic":
                  text = `<em>${text}</em>`;
                  break;
                case "underline":
                  text = `<u>${text}</u>`;
                  break;
                case "strike":
                  text = `<s>${text}</s>`;
                  break;
                case "code":
                  text = `<code>${text}</code>`;
                  break;
                case "link": {
                  const attrs =
                    (markObj.attrs as Record<string, unknown>) || {};
                  const href =
                    typeof attrs.href === "string" ? attrs.href : "#";
                  text = `<a href="${href}">${text}</a>`;
                  break;
                }
              }
            }
          }
        }
        return text;
      }

      case "bulletList": {
        const content = Array.isArray(nodeObj.content) ? nodeObj.content : [];
        const ulContent = content.map(convertNode).join("");
        return `<ul>${ulContent}</ul>`;
      }

      case "orderedList": {
        const content = Array.isArray(nodeObj.content) ? nodeObj.content : [];
        const olContent = content.map(convertNode).join("");
        return `<ol>${olContent}</ol>`;
      }

      case "listItem": {
        const content = Array.isArray(nodeObj.content) ? nodeObj.content : [];
        const liContent = content.map(convertNode).join("");
        return `<li>${liContent}</li>`;
      }

      case "blockquote": {
        const content = Array.isArray(nodeObj.content) ? nodeObj.content : [];
        const bqContent = content.map(convertNode).join("");
        return `<blockquote>${bqContent}</blockquote>`;
      }

      case "codeBlock": {
        const content = Array.isArray(nodeObj.content) ? nodeObj.content : [];
        const codeContent = content.map(convertNode).join("");
        return `<pre><code>${codeContent}</code></pre>`;
      }

      case "hardBreak":
        return "<br>";

      case "horizontalRule":
        return "<hr>";

      default: {
        // For unknown node types, try to render their content
        const content = Array.isArray(nodeObj.content) ? nodeObj.content : [];
        return content.map(convertNode).join("");
      }
    }
  };

  const contentObj = jsonContent as Record<string, unknown>;
  return convertNode(contentObj);
};
