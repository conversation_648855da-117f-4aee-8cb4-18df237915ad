{"name": "aicademy-dashboard", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "vite dev --port 3000", "build": "vite build", "lint": "biome lint ./src", "lint:fix": "pnpm biome check --write src"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "^1.1.10", "@radix-ui/react-toggle-group": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.8", "@tabler/icons-react": "^3.34.1", "@tailwindcss/vite": "^4.1.12", "@tanstack/react-form": "^1.19.2", "@tanstack/react-query": "^5.85.5", "@tanstack/react-query-devtools": "^5.85.5", "@tanstack/react-router": "^1.131.27", "@tanstack/react-router-devtools": "^1.131.27", "@tanstack/react-router-with-query": "^1.130.17", "@tanstack/react-start": "^1.131.27", "@tanstack/react-table": "^8.21.3", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "cookie-es": "^2.0.0", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "geist": "^1.4.2", "input-otp": "1.4.1", "jotai": "^2.13.1", "lucide-react": "^0.539.0", "react": "^19.1.1", "react-day-picker": "9.8.0", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-resizable-panels": "^2.1.9", "recharts": "2.15.4", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss-animate": "^1.0.7", "type-fest": "^4.41.0", "vaul": "^1.1.2", "vite": "^7.1.3", "zod": "^4.0.17", "@tiptap/core": "latest", "@tiptap/extension-color": "latest", "@tiptap/extension-placeholder": "latest", "@tiptap/extension-text-align": "latest", "@tiptap/extension-text-style": "latest", "@tiptap/extensions": "latest", "@tiptap/pm": "latest", "@tiptap/react": "latest", "@tiptap/starter-kit": "latest"}, "devDependencies": {"@biomejs/biome": "2.0.0-beta.1", "@tailwindcss/vite": "^4.1.8", "@tanstack/router-core": "^1.131.27", "@tanstack/start-client-core": "^1.131.27", "@types/google.accounts": "^0.0.16", "@types/jest": "^29.5.14", "@types/matter-js": "^0.19.8", "@types/node": "^24.3.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "autoprefixer": "^10.4.21", "husky": "^9.1.7", "jest": "^30.0.5", "lint-staged": "^16.1.5", "tailwindcss": "^4.1.12", "ts-jest": "^29.4.1", "tw-animate-css": "^1.3.7", "type-fest": "^4.41.0", "typescript": "^5.9.2", "unrs-resolver": "1.7.13", "vite-tsconfig-paths": "^5.1.4"}}